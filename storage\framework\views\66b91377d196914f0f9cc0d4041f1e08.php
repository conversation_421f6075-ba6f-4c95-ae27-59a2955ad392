

<?php $__env->startSection('page-title'); ?>
    <?php echo e(__('Create Employee')); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>"><?php echo e(__('Dashboard')); ?></a></li>
    <li class="breadcrumb-item"><a href="<?php echo e(route('employee.index')); ?>"><?php echo e(__('Employee')); ?></a></li>
    <li class="breadcrumb-item"><?php echo e(__('Create Employee')); ?></li>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><?php echo e(__('Create Employee')); ?></h5>
            </div>
            <div class="card-body">
                <form method="POST" action="<?php echo e(route('employees.store')); ?>" class="needs-validation" novalidate>
                    <?php echo csrf_field(); ?>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="name" class="form-label"><?php echo e(__('Name')); ?> <span class="text-danger">*</span></label>
                                <input type="text" class="form-control <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="name" name="name" value="<?php echo e(old('name')); ?>" required>
                                <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="email" class="form-label"><?php echo e(__('Email')); ?> <span class="text-danger">*</span></label>
                                <input type="email" class="form-control <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="email" name="email" value="<?php echo e(old('email')); ?>" required>
                                <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="password" class="form-label"><?php echo e(__('Password')); ?> <span class="text-danger">*</span></label>
                                <input type="password" class="form-control <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="password" name="password" required minlength="6">
                                <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="branch_id" class="form-label"><?php echo e(__('Branch')); ?></label>
                                <select class="form-control" id="branch_id" name="branch_id">
                                    <option value=""><?php echo e(__('Select Branch')); ?></option>
                                    <?php $__currentLoopData = $branches; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $id => $name): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($id); ?>" <?php echo e(old('branch_id') == $id ? 'selected' : ''); ?>><?php echo e($name); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="department_id" class="form-label"><?php echo e(__('Department')); ?></label>
                                <select class="form-control" id="department_id" name="department_id">
                                    <option value=""><?php echo e(__('Select Department')); ?></option>
                                    <?php $__currentLoopData = $departments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $id => $name): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($id); ?>" <?php echo e(old('department_id') == $id ? 'selected' : ''); ?>><?php echo e($name); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="designation_id" class="form-label"><?php echo e(__('Designation')); ?></label>
                                <select class="form-control" id="designation_id" name="designation_id">
                                    <option value=""><?php echo e(__('Select Designation')); ?></option>
                                    <?php $__currentLoopData = $designations; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $id => $name): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($id); ?>" <?php echo e(old('designation_id') == $id ? 'selected' : ''); ?>><?php echo e($name); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="dob" class="form-label"><?php echo e(__('Date of Birth')); ?></label>
                                <input type="date" class="form-control" id="dob" name="dob" value="<?php echo e(old('dob')); ?>">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="gender" class="form-label"><?php echo e(__('Gender')); ?></label>
                                <select class="form-control" id="gender" name="gender">
                                    <option value=""><?php echo e(__('Select Gender')); ?></option>
                                    <option value="male" <?php echo e(old('gender') == 'male' ? 'selected' : ''); ?>><?php echo e(__('Male')); ?></option>
                                    <option value="female" <?php echo e(old('gender') == 'female' ? 'selected' : ''); ?>><?php echo e(__('Female')); ?></option>
                                    <option value="other" <?php echo e(old('gender') == 'other' ? 'selected' : ''); ?>><?php echo e(__('Other')); ?></option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="phone" class="form-label"><?php echo e(__('Phone')); ?></label>
                                <input type="text" class="form-control" id="phone" name="phone" value="<?php echo e(old('phone')); ?>">
                            </div>
                        </div>
                        <div class="col-md-12">
                            <div class="form-group">
                                <label for="address" class="form-label"><?php echo e(__('Address')); ?></label>
                                <textarea class="form-control" id="address" name="address" rows="2"><?php echo e(old('address')); ?></textarea>
                            </div>
                        </div>
                    </div>
                    <hr>
                    <div class="col-12 mt-4">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h5><?php echo e(__('Module Permissions')); ?></h5>
                            <div>
                                <button type="button" class="btn btn-sm btn-outline-primary me-2" id="selectAllModules"><?php echo e(__('Select All Modules')); ?></button>
                                <button type="button" class="btn btn-sm btn-outline-secondary" id="deselectAllModules"><?php echo e(__('Deselect All')); ?></button>
                            </div>
                        </div>
                        <hr>
                        <?php $__currentLoopData = $availableModules; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $moduleKey => $moduleData): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php
                                $createPermissions = [];
                                $readPermissions = [];
                                $deletePermissions = [];
                                $otherPermissions = [];
                                if (is_array($moduleData) && isset($moduleData['permissions'])) {
                                    foreach($moduleData['permissions'] as $permission) {
                                        if (str_contains($permission, 'create')) {
                                            $createPermissions[] = $permission;
                                        } elseif (str_contains($permission, 'view') || str_contains($permission, 'show') || str_contains($permission, 'dashboard')) {
                                            $readPermissions[] = $permission;
                                        } elseif (str_contains($permission, 'delete')) {
                                            $deletePermissions[] = $permission;
                                        } else {
                                            $otherPermissions[] = $permission;
                                        }
                                    }
                                }
                            ?>
                            <div class="card mb-3">
                                <div class="card-header">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div class="form-check">
                                            <input class="form-check-input module-checkbox" type="checkbox" id="module_<?php echo e($moduleKey); ?>" name="modules[<?php echo e($moduleKey); ?>]" data-module="<?php echo e($moduleKey); ?>">
                                            <label class="form-check-label fw-bold" for="module_<?php echo e($moduleKey); ?>">
                                                <?php echo e(is_array($moduleData) && isset($moduleData['name']) ? $moduleData['name'] : $moduleKey); ?>

                                            </label>
                                        </div>
                                        <div class="btn-group" role="group" style="display: none;" id="selectButtons_<?php echo e($moduleKey); ?>">
                                            <button type="button" class="btn btn-sm btn-outline-success select-all-module" data-module="<?php echo e($moduleKey); ?>"><?php echo e(__('Select All')); ?></button>
                                            <button type="button" class="btn btn-sm btn-outline-warning select-create" data-module="<?php echo e($moduleKey); ?>"><?php echo e(__('Create')); ?></button>
                                            <button type="button" class="btn btn-sm btn-outline-info select-read" data-module="<?php echo e($moduleKey); ?>"><?php echo e(__('Read')); ?></button>
                                            <button type="button" class="btn btn-sm btn-outline-danger select-delete" data-module="<?php echo e($moduleKey); ?>"><?php echo e(__('Delete')); ?></button>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-body permissions-section" id="permissions_<?php echo e($moduleKey); ?>" style="display: none;">
                                    <?php if(count($createPermissions) > 0): ?>
                                        <div class="permission-group mb-4">
                                            <h6 class="text-success mb-2"><i class="fas fa-plus-circle"></i> <?php echo e(__('Create Permissions')); ?></h6>
                                            <div class="row">
                                                <?php $__currentLoopData = $createPermissions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $permission): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <div class="col-md-4 mb-2">
                                                        <div class="form-check">
                                                            <input class="form-check-input permission-checkbox create-permission" type="checkbox" id="permission_<?php echo e($moduleKey); ?>_<?php echo e(str_replace(' ', '_', $permission)); ?>" name="permissions[<?php echo e($moduleKey); ?>][]" value="<?php echo e($permission); ?>" data-module="<?php echo e($moduleKey); ?>" data-type="create">
                                                            <label class="form-check-label" for="permission_<?php echo e($moduleKey); ?>_<?php echo e(str_replace(' ', '_', $permission)); ?>"><?php echo e(ucwords(str_replace('_', ' ', $permission))); ?></label>
                                                        </div>
                                                    </div>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                    <?php if(count($readPermissions) > 0): ?>
                                        <div class="permission-group mb-4">
                                            <h6 class="text-info mb-2"><i class="fas fa-eye"></i> <?php echo e(__('Read/View Permissions')); ?></h6>
                                            <div class="row">
                                                <?php $__currentLoopData = $readPermissions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $permission): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <div class="col-md-4 mb-2">
                                                        <div class="form-check">
                                                            <input class="form-check-input permission-checkbox read-permission" type="checkbox" id="permission_<?php echo e($moduleKey); ?>_<?php echo e(str_replace(' ', '_', $permission)); ?>" name="permissions[<?php echo e($moduleKey); ?>][]" value="<?php echo e($permission); ?>" data-module="<?php echo e($moduleKey); ?>" data-type="read">
                                                            <label class="form-check-label" for="permission_<?php echo e($moduleKey); ?>_<?php echo e(str_replace(' ', '_', $permission)); ?>"><?php echo e(ucwords(str_replace('_', ' ', $permission))); ?></label>
                                                        </div>
                                                    </div>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                    <?php if(count($deletePermissions) > 0): ?>
                                        <div class="permission-group mb-4">
                                            <h6 class="text-danger mb-2"><i class="fas fa-trash"></i> <?php echo e(__('Delete Permissions')); ?></h6>
                                            <div class="row">
                                                <?php $__currentLoopData = $deletePermissions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $permission): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <div class="col-md-4 mb-2">
                                                        <div class="form-check">
                                                            <input class="form-check-input permission-checkbox delete-permission" type="checkbox" id="permission_<?php echo e($moduleKey); ?>_<?php echo e(str_replace(' ', '_', $permission)); ?>" name="permissions[<?php echo e($moduleKey); ?>][]" value="<?php echo e($permission); ?>" data-module="<?php echo e($moduleKey); ?>" data-type="delete">
                                                            <label class="form-check-label" for="permission_<?php echo e($moduleKey); ?>_<?php echo e(str_replace(' ', '_', $permission)); ?>"><?php echo e(ucwords(str_replace('_', ' ', $permission))); ?></label>
                                                        </div>
                                                    </div>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                    <?php if(count($otherPermissions) > 0): ?>
                                        <div class="permission-group mb-4">
                                            <h6 class="text-secondary mb-2"><i class="fas fa-cog"></i> <?php echo e(__('Other Permissions')); ?></h6>
                                            <div class="row">
                                                <?php $__currentLoopData = $otherPermissions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $permission): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <div class="col-md-4 mb-2">
                                                        <div class="form-check">
                                                            <input class="form-check-input permission-checkbox other-permission" type="checkbox" id="permission_<?php echo e($moduleKey); ?>_<?php echo e(str_replace(' ', '_', $permission)); ?>" name="permissions[<?php echo e($moduleKey); ?>][]" value="<?php echo e($permission); ?>" data-module="<?php echo e($moduleKey); ?>" data-type="other">
                                                            <label class="form-check-label" for="permission_<?php echo e($moduleKey); ?>_<?php echo e(str_replace(' ', '_', $permission)); ?>"><?php echo e(ucwords(str_replace('_', ' ', $permission))); ?></label>
                                                        </div>
                                                    </div>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                    <div class="row mt-4">
                        <div class="col-12">
                            <button type="submit" class="btn btn-primary"><?php echo e(__('Create Employee')); ?></button>
                            <a href="<?php echo e(route('users.index')); ?>" class="btn btn-secondary"><?php echo e(__('Cancel')); ?></a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('script-page'); ?>
    <script>
        $(document).ready(function() {
            // Module checkbox toggle
            $('.module-checkbox').on('change', function() {
                var module = $(this).data('module');
                var permissionsSection = $('#permissions_' + module);
                var permissionCheckboxes = permissionsSection.find('.permission-checkbox');
                var selectButtons = $('#selectButtons_' + module);
                
                if ($(this).is(':checked')) {
                    permissionsSection.show();
                    selectButtons.show();
                } else {
                    permissionsSection.hide();
                    selectButtons.hide();
                    permissionCheckboxes.prop('checked', false);
                }
            });

            // If any permission is checked, ensure module is checked
            $('.permission-checkbox').on('change', function() {
                var module = $(this).data('module');
                var moduleCheckbox = $('#module_' + module);
                var permissionCheckboxes = $('input[name="permissions[' + module + '][]"]');
                var checkedPermissions = permissionCheckboxes.filter(':checked');
                var selectButtons = $('#selectButtons_' + module);
                
                if (checkedPermissions.length > 0) {
                    moduleCheckbox.prop('checked', true);
                    $('#permissions_' + module).show();
                    selectButtons.show();
                } else {
                    moduleCheckbox.prop('checked', false);
                    $('#permissions_' + module).hide();
                    selectButtons.hide();
                }
            });

            // Select All Modules
            $('#selectAllModules').on('click', function() {
                $('.module-checkbox').prop('checked', true).trigger('change');
            });

            // Deselect All Modules
            $('#deselectAllModules').on('click', function() {
                $('.module-checkbox').prop('checked', false).trigger('change');
            });

            // Select All permissions for a specific module
            $('.select-all-module').on('click', function() {
                var module = $(this).data('module');
                $('#permissions_' + module + ' .permission-checkbox').prop('checked', true);
            });

            // Select Create permissions for a specific module
            $('.select-create').on('click', function() {
                var module = $(this).data('module');
                $('#permissions_' + module + ' .create-permission').prop('checked', true);
            });

            // Select Read permissions for a specific module
            $('.select-read').on('click', function() {
                var module = $(this).data('module');
                $('#permissions_' + module + ' .read-permission').prop('checked', true);
            });

            // Select Delete permissions for a specific module
            $('.select-delete').on('click', function() {
                var module = $(this).data('module');
                $('#permissions_' + module + ' .delete-permission').prop('checked', true);
            });
        });
    </script>
<?php $__env->stopPush(); ?> 
<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\omx-new-saas\resources\views/user/create_employee.blade.php ENDPATH**/ ?>