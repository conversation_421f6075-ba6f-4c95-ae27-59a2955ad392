
<?php
    $profile = \App\Models\Utility::get_file('uploads/avatar/');
?>
<?php $__env->startSection('page-title'); ?>
    <?php echo e(__('Manage Client')); ?>

<?php $__env->stopSection(); ?>
<?php $__env->startPush('script-page'); ?>
<?php $__env->stopPush(); ?>
<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item">
        <a href="<?php echo e(route('dashboard')); ?>"><?php echo e(__('Dashboard')); ?></a>
    </li>
    <li class="breadcrumb-item"><?php echo e(__('Client')); ?></li>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('action-btn'); ?>
    <div class="float-end">
        <a href="#"
            data-size="md"
            data-url="<?php echo e(route('clients.create')); ?>"
            data-ajax-popup="true"
            data-bs-toggle="tooltip"
            title="<?php echo e(__('Create New Client')); ?>"
            style="display:inline-flex;align-items:center;justify-content:center;width:40px;height:40px;
                    border-radius:50%;background:linear-gradient(to right, #065f46, #0f766e);color:white;
                    box-shadow:0 4px 6px rgba(0,0,0,0.1);transition:transform 0.2s ease-in-out;"
            onmouseover="this.style.transform='scale(1.1)'"
            onmouseout="this.style.transform='scale(1)'">
            <i class="ti ti-plus" style="font-size:16px;"></i>
         </a>

    </div>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>
<div class="table-responsive">
    <table class="table table-striped align-middle mb-0">
        <thead class="table-light">
            <tr>
                <th><?php echo e(__('Name')); ?></th>
                <th><?php echo e(__('Email')); ?></th>
                <th><?php echo e(__('Deals')); ?></th>
                <th><?php echo e(__('Projects')); ?></th>
                <th><?php echo e(__('Last Login')); ?></th>
                <th><?php echo e(__('Actions')); ?></th>
            </tr>
        </thead>
        <tbody>
            <?php $__currentLoopData = $clients; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $client): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <tr>
                    <td class="fw-semibold">
                        <div class="d-flex align-items-center gap-2">
                            <img avatar="<?php echo e($client->name); ?>" alt="client-image" class="rounded-circle border border-primary" style="width:36px;height:36px;object-fit:cover;">
                            <span><?php echo e($client->name); ?></span>
                        </div>
                    </td>
                    <td class="text-break"><?php echo e($client->email); ?></td>
                    <td>
                        <?php echo e($client->clientDeals ? $client->clientDeals->count() : 0); ?>

                    </td>
                    <td>
                        <?php echo e($client->clientProjects ? $client->clientProjects->count() : 0); ?>

                    </td>
                    <td>
                        <?php if($client->last_login_at): ?>
                            <?php echo e(\Carbon\Carbon::parse($client->last_login_at)->format('M d, Y \a\t H:i')); ?>

                        <?php elseif($client->created_at): ?>
                            <?php echo e(\Carbon\Carbon::parse($client->created_at)->format('M d, Y \a\t H:i')); ?>

                        <?php else: ?>
                            <span class="text-muted"><?php echo e(__('N/A')); ?></span>
                        <?php endif; ?>
                    </td>
                    <td>
                        <div class="btn-group">
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('edit client')): ?>
                            <a href="#"
                                data-size="md"
                                data-url="<?php echo e(route('clients.edit', $client->id)); ?>"
                                data-ajax-popup="true"
                                data-bs-toggle="tooltip"
                                title="<?php echo e(__('Edit Client')); ?>"
                                style="display:inline-flex;align-items:center;justify-content:center;width:40px;height:40px;
                                        border-radius:50%;background:linear-gradient(to right, #065f46, #0f766e);color:white;
                                        box-shadow:0 4px 6px rgba(0,0,0,0.1);transition:transform 0.2s ease-in-out;"
                                onmouseover="this.style.transform='scale(1.1)'"
                                onmouseout="this.style.transform='scale(1)'">
                                <i class="ti ti-pencil" style="font-size:16px;"></i>
                            </a>

                            <?php endif; ?>
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('delete client')): ?>
                                <?php echo Form::open(['method' => 'DELETE', 'route' => ['clients.destroy', $client['id']], 'id' => 'delete-form-' . $client['id'], 'style' => 'display:inline-block']); ?>

                                <button type="button"
                                    class="btn btn-sm bs-pass-para-delete"
                                    data-bs-toggle="tooltip"
                                    title="<?php echo e($client->delete_status != 0 ? __('Delete') : __('Restore')); ?>"
                                    data-confirm-text="<?php echo e($client->delete_status != 0 ? __('Are you sure you want to delete this client?') : __('Are you sure you want to restore this client?')); ?>"
                                    style="display:inline-flex;align-items:center;justify-content:center;width:40px;height:40px;
                                            border-radius:50%;background:linear-gradient(to right, #b91c1c, #dc2626);color:white;
                                            box-shadow:0 4px 6px rgba(0,0,0,0.1);transition:transform 0.2s ease-in-out; margin-left:5px;"
                                    onmouseover="this.style.transform='scale(1.1)'"
                                    onmouseout="this.style.transform='scale(1)'">
                                    <i class="ti ti-trash" style="font-size:16px;"></i>
                                </button>
                                <?php echo Form::close(); ?>

                            <?php endif; ?>
                            <?php if($client->is_enable_login == 1): ?>
                            <a href="<?php echo e(route('users.login', \Crypt::encrypt($client->id))); ?>"
                                class="btn btn-sm"
                                data-bs-toggle="tooltip"
                                title="<?php echo e(__('Login Disable')); ?>"
                                style="display:inline-flex;align-items:center;justify-content:center;width:40px;height:40px;
                                        border-radius:50%;background:linear-gradient(to right, #065f46, #0f766e);color:white;
                                        box-shadow:0 4px 6px rgba(0,0,0,0.1);transition:transform 0.2s ease-in-out; margin-left:5px;"
                                onmouseover="this.style.transform='scale(1.1)'"
                                onmouseout="this.style.transform='scale(1)'">
                                <i class="ti ti-road-sign" style="font-size:16px;"></i>
                            </a>
                            <?php elseif($client->is_enable_login == 0 && $client->password == null): ?>
                            <a href="#"
                                data-url="<?php echo e(route('clients.reset', \Crypt::encrypt($client->id))); ?>"
                                data-ajax-popup="true"
                                data-size="md"
                                data-bs-toggle="tooltip"
                                title="<?php echo e(__('New Password')); ?>"
                                class="login_enable"
                                style="display:inline-flex;align-items:center;justify-content:center;width:40px;height:40px;
                                        border-radius:50%;background:linear-gradient(to right, #065f46, #0f766e);color:white;
                                        box-shadow:0 4px 6px rgba(0,0,0,0.1);transition:transform 0.2s ease-in-out; margin-left:5px;"
                                onmouseover="this.style.transform='scale(1.1)'"
                                onmouseout="this.style.transform='scale(1)'">
                                <i class="ti ti-road-sign" style="font-size:16px;"></i>
                             </a>
                            <?php else: ?>
                            <a href="<?php echo e(route('users.login', \Crypt::encrypt($client->id))); ?>"
                                data-bs-toggle="tooltip"
                                title="<?php echo e(__('Login Enable')); ?>"
                                style="display:inline-flex;align-items:center;justify-content:center;width:40px;height:40px;
                                        border-radius:50%;background:linear-gradient(to right, #065f46, #0f766e);color:white;
                                        box-shadow:0 4px 6px rgba(0,0,0,0.1);transition:transform 0.2s ease-in-out; margin-left:5px;"
                                onmouseover="this.style.transform='scale(1.1)'"
                                onmouseout="this.style.transform='scale(1)'">
                                <i class="ti ti-road-sign" style="font-size:16px;"></i>
                            </a>
                            <?php endif; ?>
                            <a href="#"
                                data-url="<?php echo e(route('clients.reset', \Crypt::encrypt($client->id))); ?>"
                                data-ajax-popup="true"
                                data-bs-toggle="tooltip"
                                title="<?php echo e(__('Reset Password')); ?>"
                                style="display:inline-flex;align-items:center;justify-content:center;width:40px;height:40px;
                                        border-radius:50%;background:linear-gradient(to right, #065f46, #0f766e);color:white;
                                        box-shadow:0 4px 6px rgba(0,0,0,0.1);transition:transform 0.2s ease-in-out; margin-left:5px;"
                                onmouseover="this.style.transform='scale(1.1)'"
                                onmouseout="this.style.transform='scale(1)'">
                                <i class="ti ti-adjustments" style="font-size:16px;"></i>
                            </a>
                        </div>
                    </td>
                </tr>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </tbody>
    </table>
</div>
<!-- Standard Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">
                    <i class="ti ti-alert-triangle text-danger me-2"></i>
                    <?php echo e(__('Delete Confirmation')); ?>

                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-danger d-flex align-items-center mb-3">
                    <i class="ti ti-alert-circle me-2"></i>
                    <div>
                        <strong><?php echo e(__('Warning!')); ?></strong> <?php echo e(__('This action cannot be undone.')); ?>

                    </div>
                </div>
                <p id="deleteConfirmText" class="mb-3">
                    <?php echo e(__('Are you sure you want to delete this client?')); ?>

                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <?php echo e(__('Cancel')); ?>

                </button>
                <button type="button" class="btn btn-danger" id="deleteConfirmBtn">
                    <i class="ti ti-trash me-1"></i>
                    <?php echo e(__('Delete')); ?>

                </button>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('script-page'); ?>
    <script>
        $(document).on('change', '#password_switch', function() {
            if ($(this).is(':checked')) {
                $('.ps_div').removeClass('d-none');
                $('#password').attr("required", true);

            } else {
                $('.ps_div').addClass('d-none');
                $('#password').val(null);
                $('#password').removeAttr("required");
            }
        });
        $(document).on('click', '.login_enable', function() {
            setTimeout(function() {
                $('.modal-body').append($('<input>', {
                    type: 'hidden',
                    val: 'true',
                    name: 'login_enable'
                }));
            }, 2000);
        });
    </script>
    <script>
        // Standard delete modal functionality
        $(document).on('click', '.bs-pass-para-delete', function(e) {
            e.preventDefault();
            e.stopPropagation();
            var el = $(this);
            var form = el.closest('form');
            var confirmText = el.data('confirm-text') || 'Are you sure you want to delete this client?';
            // Set the confirmation text in the modal
            $('#deleteConfirmText').text(confirmText);
            // Store the form reference for later submission
            $('#deleteModal').attr('data-form-id', form.attr('id'));
            // Show the modal
            $('#deleteModal').modal('show');
        });
        // Handle confirmation button click for standard delete
        $(document).on('click', '#deleteConfirmBtn', function() {
            var modal = $('#deleteModal');
            var formId = modal.attr('data-form-id');
            var form = $('#' + formId);
            modal.modal('hide');
            if (form.length) {
                form.submit();
            } else {
                console.error('Form not found:', formId);
            }
        });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\omx-new-saas\resources\views/clients/index.blade.php ENDPATH**/ ?>